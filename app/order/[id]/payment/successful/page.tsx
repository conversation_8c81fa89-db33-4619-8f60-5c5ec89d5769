'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import Link from 'next/link'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  paymentMethod: string
  paymentConfirmed: boolean
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
  seller: {
    id: string
    name: string
  }
  shippingAddress: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
}

export default function PaymentSuccessfulPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)

  const orderId = params.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查订单状态和支付状态
      if (!orderData.paymentConfirmed) {
        // 支付未确认，重定向到支付页面
        router.push(`/order/${orderId}/payment/${orderData.paymentMethod}`)
        return
      }
      
      setOrder(orderData)
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleContactSeller = () => {
    if (order) {
      // 跳转到聊天页面
      router.push(`/chat?userId=${order.seller.id}`)
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'binancepay-QRcode':
        return '币安二维码支付'
      case 'bsc-pay':
        return 'BNB Smart Chain支付'
      case 'balance-pay':
        return '余额支付'
      default:
        return method
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          {/* 支付成功提示 */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-green-600 mb-2">支付成功！</h1>
            <p className="text-gray-600">您的订单已成功支付，卖家将尽快为您发货</p>
          </div>

          {/* 订单信息 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">订单详情</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <span className="text-sm text-gray-600">订单号</span>
                  <p className="font-mono text-sm">{order.orderNumber}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">支付方式</span>
                  <p className="text-sm">{getPaymentMethodText(order.paymentMethod)}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">支付金额</span>
                  <p className="text-lg font-semibold text-green-600">¥{order.totalAmount.toFixed(2)}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">订单状态</span>
                  <p className="text-sm">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      已支付
                    </span>
                  </p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex items-center space-x-4">
                  <img
                    src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
                    alt={order.product.title}
                    className="w-16 h-16 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{order.product.title}</h4>
                    <p className="text-gray-600 text-sm">卖家：{order.product.seller.name}</p>
                    <p className="text-gray-600 text-sm">数量：{order.metadata.quantity}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">¥{order.productPrice.toFixed(2)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 收货地址 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">收货地址</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">{order.shippingAddress.name}</span>
                <span className="text-gray-600">{order.shippingAddress.phone}</span>
              </div>
              <div className="text-gray-700">
                {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district} {order.shippingAddress.detail}
              </div>
            </div>
          </div>

          {/* 温馨提示 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">温馨提示</h3>
            <div className="bg-blue-50 rounded-lg p-4">
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                <li>卖家将在收到订单后尽快为您安排发货</li>
                <li>您可以通过"联系卖家"按钮与卖家沟通</li>
                <li>如有任何问题，请及时联系客服</li>
                <li>收到商品后请及时确认收货</li>
              </ul>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={handleContactSeller}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                联系卖家
              </button>
              <Link
                href="/orders"
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center"
              >
                查看订单
              </Link>
            </div>
            
            <Link
              href="/"
              className="block w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center"
            >
              返回首页
            </Link>
          </div>

          {/* 商品推荐区域（暂时隐藏，后续实现） */}
          {/* 
          <div className="mt-12 pt-8 border-t">
            <h3 className="font-medium mb-4">为您推荐</h3>
            <div className="text-center text-gray-500 py-8">
              基于您的浏览记录推荐商品（功能开发中）
            </div>
          </div>
          */}
        </div>
      </div>
    </div>
  )
}
