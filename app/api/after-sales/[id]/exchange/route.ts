import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 处理换货申请
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { 
      exchangeProductId, 
      shippingCompany, 
      trackingNumber, 
      returnTrackingNumber,
      exchangeNotes 
    } = body

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            id: true,
            sellerId: true,
            buyerId: true,
            productId: true,
            orderNumber: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有卖家可以处理换货
    if (afterSalesRequest.order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限处理此换货申请' },
        { status: 403 }
      )
    }

    // 检查申请类型和状态
    if (afterSalesRequest.type !== 'EXCHANGE') {
      return NextResponse.json(
        { error: '该申请不是换货申请' },
        { status: 400 }
      )
    }

    if (afterSalesRequest.status !== 'APPROVED') {
      return NextResponse.json(
        { error: '该换货申请尚未同意' },
        { status: 400 }
      )
    }

    // 验证换货商品
    if (exchangeProductId) {
      const exchangeProduct = await prisma.product.findUnique({
        where: { id: exchangeProductId },
        select: {
          id: true,
          sellerId: true,
          status: true,
          stock: true
        }
      })

      if (!exchangeProduct) {
        return NextResponse.json(
          { error: '换货商品不存在' },
          { status: 400 }
        )
      }

      if (exchangeProduct.sellerId !== session.user.id) {
        return NextResponse.json(
          { error: '换货商品不属于当前卖家' },
          { status: 400 }
        )
      }

      if (exchangeProduct.status !== 'ACTIVE' || exchangeProduct.stock <= 0) {
        return NextResponse.json(
          { error: '换货商品不可用或库存不足' },
          { status: 400 }
        )
      }
    }

    // 验证运单号
    if (trackingNumber && !trackingNumber.trim()) {
      return NextResponse.json(
        { error: '请提供有效的运单号' },
        { status: 400 }
      )
    }

    // 更新售后申请状态
    const updateData: any = {
      status: 'PROCESSING',
      processedAt: new Date()
    }

    if (exchangeProductId) {
      updateData.exchangeProductId = exchangeProductId
    }

    if (trackingNumber) {
      updateData.exchangeTrackingNumber = trackingNumber.trim()
    }

    if (exchangeNotes) {
      updateData.adminNotes = exchangeNotes.trim()
    }

    const updatedRequest = await prisma.afterSalesRequest.update({
      where: { id },
      data: updateData
    })

    // 如果提供了换货商品，减少库存
    if (exchangeProductId) {
      await prisma.product.update({
        where: { id: exchangeProductId },
        data: {
          stock: {
            decrement: 1
          }
        }
      })
    }

    // 创建操作日志
    await prisma.orderLog.create({
      data: {
        orderId: afterSalesRequest.orderId,
        operatorId: session.user.id,
        action: 'PROCESS_EXCHANGE',
        description: `处理换货申请${trackingNumber ? `，运单号：${trackingNumber}` : ''}`
      }
    })

    // TODO: 发送换货处理通知
    // await sendExchangeProcessingNotification(afterSalesRequest.order.buyerId, updatedRequest)

    return NextResponse.json({
      success: true,
      message: '换货处理成功',
      request: updatedRequest
    })

  } catch (error) {
    console.error('处理换货申请失败:', error)
    return NextResponse.json(
      { error: '处理换货申请失败' },
      { status: 500 }
    )
  }
}

// 完成换货
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { returnReceived, exchangeCompleted } = body

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限操作此换货申请' },
        { status: 403 }
      )
    }

    // 检查状态
    if (afterSalesRequest.status !== 'PROCESSING') {
      return NextResponse.json(
        { error: '换货申请不在处理中状态' },
        { status: 400 }
      )
    }

    let updateData: any = {}
    let actionDescription = ''

    if (returnReceived && isSeller) {
      // 卖家确认收到退货
      updateData.adminNotes = (updateData.adminNotes || '') + '\n卖家已确认收到退货商品'
      actionDescription = '卖家确认收到退货'
    }

    if (exchangeCompleted) {
      // 完成换货
      updateData.status = 'COMPLETED'
      updateData.completedAt = new Date()
      actionDescription = '换货完成'
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: '无有效的操作' },
        { status: 400 }
      )
    }

    // 更新售后申请
    const updatedRequest = await prisma.afterSalesRequest.update({
      where: { id },
      data: updateData
    })

    // 创建操作日志
    await prisma.orderLog.create({
      data: {
        orderId: afterSalesRequest.orderId,
        operatorId: session.user.id,
        action: 'UPDATE_EXCHANGE_STATUS',
        description: actionDescription
      }
    })

    return NextResponse.json({
      success: true,
      message: actionDescription + '成功',
      request: updatedRequest
    })

  } catch (error) {
    console.error('更新换货状态失败:', error)
    return NextResponse.json(
      { error: '更新换货状态失败' },
      { status: 500 }
    )
  }
}

// 获取换货信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取售后申请
    const afterSalesRequest = await prisma.afterSalesRequest.findUnique({
      where: { id },
      include: {
        order: {
          select: {
            sellerId: true,
            buyerId: true,
            productId: true,
            product: {
              select: {
                title: true,
                images: true,
                price: true
              }
            }
          }
        },
        exchangeProduct: {
          select: {
            id: true,
            title: true,
            images: true,
            price: true
          }
        }
      }
    })

    if (!afterSalesRequest) {
      return NextResponse.json(
        { error: '售后申请不存在' },
        { status: 404 }
      )
    }

    // 检查权限
    const isBuyer = afterSalesRequest.order.buyerId === session.user.id
    const isSeller = afterSalesRequest.order.sellerId === session.user.id
    
    if (!isBuyer && !isSeller) {
      return NextResponse.json(
        { error: '无权限查看此换货信息' },
        { status: 403 }
      )
    }

    // 获取可换货的商品列表（仅卖家可见）
    let availableProducts = []
    if (isSeller) {
      availableProducts = await prisma.product.findMany({
        where: {
          sellerId: session.user.id,
          status: 'ACTIVE',
          stock: { gt: 0 },
          id: { not: afterSalesRequest.order.productId } // 排除原商品
        },
        select: {
          id: true,
          title: true,
          images: true,
          price: true,
          stock: true
        },
        take: 20 // 限制数量
      })
    }

    return NextResponse.json({
      request: afterSalesRequest,
      originalProduct: afterSalesRequest.order.product,
      exchangeProduct: afterSalesRequest.exchangeProduct,
      availableProducts,
      canProcess: isSeller && afterSalesRequest.status === 'APPROVED',
      canComplete: (isBuyer || isSeller) && afterSalesRequest.status === 'PROCESSING'
    })

  } catch (error) {
    console.error('获取换货信息失败:', error)
    return NextResponse.json(
      { error: '获取换货信息失败' },
      { status: 500 }
    )
  }
}
